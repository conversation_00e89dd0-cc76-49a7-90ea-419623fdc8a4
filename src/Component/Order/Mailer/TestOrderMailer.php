<?php

namespace App\Component\Order\Mailer;

use Peracto\Component\Order\Mailer\OrderMailerInterface;
use Peracto\Component\Order\Model\OrderInterface;
use Symfony\Component\Mailer\MailerInterface;
use Symfony\Component\Mime\Email;
use Twig\Environment;

readonly class TestOrderMailer implements OrderMailerInterface
{
    public function __construct(
        private MailerInterface $mailer,
        private Environment $twig,
    ) {
    }

    public function sendOrderConfirmationEmail(OrderInterface $order): void
    {
        $email = (new Email())
            ->from('<EMAIL>')
            ->to('<EMAIL>')
            ->subject('Order Confirmation')
            ->html($this->twig->render('@PeractoOrderBundle/email/order-confirmation.html.twig', [
                'order' => $order,
                'subject' => 'Order Confirmation',
            ]));

        $this->mailer->send($email);
    }

    public function sendOrderHistoryUpdateEmail(OrderInterface $order): void
    {
        // Not implemented for testing
    }
}
