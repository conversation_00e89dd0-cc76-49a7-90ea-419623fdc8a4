<?php

namespace App\EventSubscriber;

use ApiPlatform\Symfony\EventListener\EventPriorities;
use Peracto\Component\Payment\Event\PaymentTransactionCompleteEvent;
use Peracto\Component\Payment\Model\PaymentTransactionInterface;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Event\ViewEvent;
use Symfony\Component\HttpKernel\KernelEvents;

readonly class PaymentTransactionApiSubscriber implements EventSubscriberInterface
{
    public function __construct(
        private EventDispatcherInterface $eventDispatcher,
    ) {
    }

    public static function getSubscribedEvents(): array
    {
        return [
            KernelEvents::VIEW => ['onKernelView', EventPriorities::POST_WRITE],
        ];
    }

    public function onKernelView(ViewEvent $event): void
    {
        $controllerResult = $event->getControllerResult();
        $request = $event->getRequest();

        if (!($controllerResult instanceof PaymentTransactionInterface)) {
            return;
        }

        if (!$request->isMethod(Request::METHOD_POST)) {
            return;
        }

        if ($controllerResult->getStatus() !== 'complete') {
            return;
        }

        // Dispatch the PaymentTransactionCompleteEvent
        $this->eventDispatcher->dispatch(
            new PaymentTransactionCompleteEvent($controllerResult)
        );
    }
}
