<?php

namespace App\EventSubscriber;

use App\Message\Order\UploadOrderToS3Message;
use Peracto\Component\Order\Mailer\OrderMailerInterface;
use Peracto\Component\Order\Model\OrderInterface;
use Peracto\Component\Payment\Event\PaymentTransactionCompleteEvent;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Messenger\MessageBusInterface;

readonly class OrderCreatedSubscriber implements EventSubscriberInterface
{
    public function __construct(
        private MessageBusInterface $messageBus,
        private OrderMailerInterface $orderMailer,
    ) {
    }

    public static function getSubscribedEvents(): array
    {
        return [
            PaymentTransactionCompleteEvent::class => ['onPaymentTransactionComplete'],
        ];
    }

    public function onPaymentTransactionComplete(PaymentTransactionCompleteEvent $event): void
    {
        $order = $event->getPaymentTransaction()->getOrder();

        if (!($order instanceof OrderInterface)) {
            return;
        }

        // Send order confirmation email
        $this->orderMailer->sendOrderConfirmationEmail($order);

        // Upload order to S3
        $this->messageBus->dispatch(
            new UploadOrderToS3Message($order->getId()),
        );
    }
}
