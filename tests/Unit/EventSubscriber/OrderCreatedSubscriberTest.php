<?php

namespace App\Tests\Unit\EventSubscriber;

use App\Entity\OrderInterface;
use App\EventSubscriber\OrderCreatedSubscriber;
use App\Message\Order\UploadOrderToS3Message;
use Peracto\Component\Order\Mailer\OrderMailerInterface;
use Peracto\Component\Payment\Event\PaymentTransactionCompleteEvent;
use Peracto\Component\Payment\Model\PaymentTransaction;
use Peracto\Testing\TestCase\OptimizedPHPUnitTestCase;
use Prophecy\Argument;
use Symfony\Component\Messenger\Envelope;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Messenger\Stamp\StampInterface;

class OrderCreatedSubscriberTest extends OptimizedPHPUnitTestCase
{
    public function test_event_is_not_dispatched_when_order_not_found()
    {
        $paymentTransaction = new PaymentTransaction();
        $order = $this->prophesize(OrderInterface::class);
        $order->getId()->willReturn(17);
        $messageBus = $this->prophesize(MessageBusInterface::class);
        $orderMailer = $this->prophesize(OrderMailerInterface::class);

        $sut = new OrderCreatedSubscriber($messageBus->reveal(), $orderMailer->reveal());
        $sut->onPaymentTransactionComplete(
            new PaymentTransactionCompleteEvent($paymentTransaction)
        );

        $messageBus->dispatch(Argument::any())->shouldNotBeCalled();
        $orderMailer->sendOrderConfirmationEmail(Argument::any())->shouldNotBeCalled();
    }

    public function test_when_payment_is_completed_event_is_dispatched_to_upload_order_file()
    {
        $order = $this->prophesize(\Peracto\Component\Order\Model\OrderInterface::class);
        $order->getId()->willReturn(17);

        $paymentTransaction = new PaymentTransaction();
        $paymentTransaction->setOrder($order->reveal());

        $event = new PaymentTransactionCompleteEvent($paymentTransaction);

        $message = new UploadOrderToS3Message(17);
        $messageBus = $this->prophesize(MessageBusInterface::class);
        $orderMailer = $this->prophesize(OrderMailerInterface::class);

        $stamp = $this->prophesize(StampInterface::class);
        $envelope = new Envelope($message, [$stamp->reveal()]);

        $sut = new OrderCreatedSubscriber($messageBus->reveal(), $orderMailer->reveal());

        $orderMailer->sendOrderConfirmationEmail($order->reveal())
            ->shouldBeCalledOnce();

        $messageBus->dispatch($message)
            ->shouldBeCalledOnce()
            ->willReturn($envelope);

        $sut->onPaymentTransactionComplete($event);
    }
}
