doctrine:
  dbal:
    driver: 'pdo_mysqli'
    server_version: '8.0'
    charset: utf8mb4
    default_table_options:
      charset: utf8mb4
      collate: utf8mb4_unicode_ci
    url: '%env(resolve:DATABASE_URL)%'

  orm:
    auto_generate_proxy_classes: true
    naming_strategy: doctrine.orm.naming_strategy.underscore_number_aware
    auto_mapping: true
    mappings:
      App:
        is_bundle: false
        type: attribute
        dir: '%kernel.project_dir%/src/Entity'
        prefix: 'App\Entity'
        alias: App

when@prod: &prod
  doctrine:
    dbal:
      url: 'mysql://%env(DB_USER)%:%env(resolve:DB_PASSWORD)%@%env(resolve:DB_HOST)%:%env(resolve:DB_PORT)%/%env(resolve:DB_NAME)%'
    orm:
      auto_generate_proxy_classes: false
      metadata_cache_driver:
        type: pool
        pool: doctrine.system_cache_pool
      query_cache_driver:
        type: pool
        pool: doctrine.system_cache_pool
      result_cache_driver:
        type: pool
        pool: doctrine.result_cache_pool

  framework:
    cache:
      pools:
        doctrine.result_cache_pool:
          adapter: cache.app
        doctrine.system_cache_pool:
          adapter: cache.system

when@staging: *prod

