parameters:
    env(AWS_LAMBDA_FUNCTION_VERSION): 0

api_platform:
  patch_formats:
    json: ['application/merge-patch+json']
  swagger:
    versions: [3]

when@staging: &staging
  api_platform:
    enable_swagger_ui: true
    enable_re_doc: true
    formats:
      jsonld: ['application/ld+json']
      json: ['application/json']
      multipart: ['multipart/form-data']
      html: ['text/html']

when@prod: &prod
    api_platform:
        version: '%env(resolve:AWS_LAMBDA_FUNCTION_VERSION)%'
        enable_swagger_ui: false
        enable_re_doc: false
