security:
  password_hashers:
    App\Entity\User:
      algorithm: auto

  providers:
    user_bundle_provider:
      entity:
        class: App\Entity\User
        property: email
    guest_user_bundle_provider:
      entity:
        class: Peracto\Component\User\Model\GuestUser
        property: username
    all_users:
      chain:
        providers: ['user_bundle_provider', 'guest_user_bundle_provider']

  hide_user_not_found: false

  firewalls:
    api:
      user_checker: Peracto\Component\User\Security\UserChecker
      stateless: true
      provider: all_users
      json_login:
        check_path: /auth
        username_path: email
        password_path: password
        success_handler: lexik_jwt_authentication.handler.authentication_success
        failure_handler: lexik_jwt_authentication.handler.authentication_failure
      guest: ~
      entry_point: jwt
      jwt: ~
      refresh_jwt:
        check_path: gesdinet_jwt_refresh_token
        provider: all_users

  access_control:
