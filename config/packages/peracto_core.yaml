peracto_core:
  api_platform:
    mapping:
      extension_paths:
        - '%kernel.project_dir%/config/api_platform/api_resources.yaml'
  third_party_mailing:
    mailchimp:
      api_key: '%env(resolve:APP_MAILCHIMP_API_KEY)%'
      server_prefix: '%env(resolve:APP_MAILCHIMP_SERVER_PREFIX)%'
      marketing_opt_in_list_id: '%env(resolve:APP_MAILCHIMP_LIST_ID)%'
when@prod: &prod
  peracto_core:
    sitemap:
      storage:
        s3:
          directory_name: sitemaps
          bucket_name: '%env(resolve:ASSETS_AWS_BUCKET)%'

when@staging: *prod

when@dev: &dev
  peracto_core:
    sitemap:
      storage:
        s3:
          directory_name: null
          bucket_name: null
        local:
          path: /tmp/keylite/sitemaps

when@test: *dev
