nelmio_cors:
  defaults:
    origin_regex: true
    allow_credentials: true
    allow_origin: ['%env(CORS_ALLOW_ORIGIN)%']
    allow_methods: ['GET', 'OPTIONS', 'POST', 'PUT', 'PATCH', 'DELETE']
    allow_headers: ['Content-Type', 'Authorization', 'peracto-index-content', 'peracto-index-product', 'order-again-id', 'optin', 'peracto-send-user-order-history-email']
    expose_headers: ['Link']
    max_age: 3600
  paths:
    '^/': null
