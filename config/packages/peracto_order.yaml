peracto_order:
  entities:
    order:
      classes:
        model: App\Entity\Order
    order_line:
      classes:
        model: App\Entity\OrderLine
    order_unit:
      classes:
        model: App\Entity\OrderUnit
    basket_line:
      classes:
        model: App\Entity\NoneDoctrine\BasketLine
    promotion_codes:
      classes:
        model: App\Entity\PromotionCode
  order:
    api_resource_class: App\Component\Order\Dto\Order
    reference_builder:
      prefix: 'W'
  payment:
    pay_on_account:
      enabled: true
      type: Peracto\Component\Payment\Types\PayOnAccount
      type_key: payOnAccount
    braintree:
      enabled: true
      type_key: braintree
      type: Peracto\Component\Payment\Types\Braintree
      environment: '%env(resolve:BRAINTREE_ENVIRONMENT)%'
      merchant_id: '%env(resolve:BRAINTREE_MERCHANT_ID)%'
      public_key: '%env(resolve:BRAINTREE_PUBLIC_KEY)%'
      private_key: '%env(resolve:BRAINTREE_PRIVATE_KEY)%'
      google_pay_merchant_id: '%env(resolve:BRAINTREE_GOOGLE_PAY_MERCHANT_ID)%'
      three_d_secure: '%env(resolve:BRAINTREE_3D_SECURE)%'
      merchant_account_ids:
        GBP: '%env(resolve:BRAINTREE_MERCHANT_ACCOUNT_ID_GBP)%'
        EUR: '%env(resolve:BRAINTREE_MERCHANT_ACCOUNT_ID_EUR)%'
  basket:
    order_conversion_sections:
      - Peracto\Component\Basket\Converter\OrderConvertSection\BillingSection
      - Peracto\Component\Basket\Converter\OrderConvertSection\DateSection
      - Peracto\Component\Basket\Converter\OrderConvertSection\DeliverySection
      - App\Component\Basket\Converter\OrderConvertSection\DiscountSection
      - Peracto\Component\Basket\Converter\OrderConvertSection\OrderUnitSection
      - Peracto\Component\Basket\Converter\OrderConvertSection\ShippingSection
      - Peracto\Component\Basket\Converter\OrderConvertSection\StatusSection
      - Peracto\Component\Basket\Converter\OrderConvertSection\UserSection
    put_promotion_add_input: App\Dto\PromoCodeInput
  prices:
    currency_based_on_delivery_country: true
    tax_based_on_delivery_country: true
