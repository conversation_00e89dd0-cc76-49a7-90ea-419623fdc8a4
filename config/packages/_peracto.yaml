imports:
  - { resource: '@PeractoBackgroundBundle/Resources/config/app/messenger.yaml' }
  - { resource: '@PeractoCoreBundle/Resources/config/app/api_platform.yaml' }
  - { resource: '@PeractoCoreBundle/Resources/config/app/config.yaml' }
  - { resource: '@PeractoCoreBundle/Resources/config/app/framework.yaml' }
  - { resource: '@PeractoCoreBundle/Resources/config/app/messenger.yaml' }
  - { resource: '@PeractoCoreBundle/Resources/config/app/nelmio_cors.yaml' }
  - { resource: '@PeractoTaskBundle/Resources/config/app/messenger.yaml' }

when@prod:
  imports:
    - { resource: '@PeractoCoreBundle/Resources/config/app/prod/api_platform.yaml' }
