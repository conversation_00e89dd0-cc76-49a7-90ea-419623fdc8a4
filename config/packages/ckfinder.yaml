ckfinder:
  connector:
    licenseName: '%env(CKFINDER_LICENSE_NAME)%'
    licenseKey: '%env(CKFINDER_LICENSE_KEY)%'

    authenticationClass: App\CKFinder\Auth

    csrfProtection: false

    debug: true

    images:
      maxWidth: 2500
      maxHeight: 2500
      quality: 100

    resourceTypes:
      Files:
        name: 'Files'
        directory: 'files'
        maxSize: 0
        allowedExtensions: '7z,aiff,asf,avi,bmp,csv,doc,docx,fla,flv,gif,gz,gzip,jpeg,jpg,mid,mov,mp3,mp4,mpc,mpeg,mpg,ods,odt,pdf,png,ppt,pptx,pxd,qt,ram,rar,rm,rmi,rmvb,rtf,sdc,sitd,swf,sxc,sxw,tar,tgz,tif,tiff,txt,vsd,wav,wma,wmv,xls,xlsx,zip,svg,webp,avif'
        deniedExtensions: ''
        backend: default

      Images:
        name: 'Images'
        directory: 'images'
        maxSize: 0
        allowedExtensions: 'bmp,gif,jpeg,jpg,png,svg,webp,avif'
        deniedExtensions: ''
        backend: default

    accessControl:
      - role: '*'
        resourceType: '*'
        folder: '/'
        IMAGE_RESIZE: false
        IMAGE_RESIZE_CUSTOM: false

    htmlExtensions: [html, htm, xml, js, svg]

when@prod: &prod
  ckfinder:
    connector:
      # Setting this to just error_log so that logs are not store in the assets bucket
      debugLoggers:
        - error_log

      # This will mean all thumbnails will be stored in the assets bucket
      privateDir:
        backend: default

      backends:
        default:
          name: default
          adapter: 's3'
          bucket: '%env(resolve:ASSETS_AWS_BUCKET)%'
          region: '%env(AWS_REGION)%'
          visibility: 'public'
          baseUrl: '%env(resolve:ASSETS_AWS_BASE_URL)%'
          root: ''

when@staging: *prod
