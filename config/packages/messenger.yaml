framework:
  messenger:
    transports:
       sync: 'sync://'

    routing:
      'Peracto\Component\Indexer\Message\IndexMessage': sync

    buses:
      messenger.bus.default:
        middleware:
          # each time a message is handled, the Doctrine connection
          # is "pinged" and reconnected if it's closed. Useful
          # if your workers run for a long time and the database
          # connection is sometimes lost
          - doctrine_ping_connection

          # After handling, the Doctrine connection is closed,
          # which can free up database connections in a worker,
          # instead of keeping them open forever
          - doctrine_close_connection

          - Peracto\Component\Task\Message\Middleware\TaskMessageMiddleware

when@test:
  framework:
    messenger:
      transports:
        sync: 'sync://'
      routing:
        'Peracto\Component\DataTransfer\Import\Message\ImportMessageInterface': sync
        'Peracto\Component\Task\Message\TaskMessageInterface': sync
        'Peracto\Component\Indexer\Message\IndexMessage': sync
        'Peracto\Component\Notification\Message\ProductNotificationMessageInterface': sync
        'Peracto\Component\DataTransfer\Import\Message\Event\ImportMessageHandlerSuccessEvent': sync
        'Peracto\Component\DataTransfer\Import\Message\Event\ImportMessageHandlerFailureEvent': sync
        'Peracto\Component\Payment\Message\V12FinanceCheckApplicationMessage': sync
