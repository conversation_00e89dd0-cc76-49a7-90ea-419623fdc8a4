parameters:
  payment_braintree_merchant_account_ids:
    GBP: 'gbp_merchant_account_id'
    EUR: 'eur_merchant_account_id'
  peracto_order.payment.braintree.merchant_account_ids_wrong:
    GPB: 'gbp_merchant_account_id'
    EUR: 'eur_merchant_account_id'

services:
  # default configuration for services in *this* file
  _defaults:
    autowire: true      # Automatically injects dependencies in your services.
    autoconfigure: true # Automatically registers your services as commands, event subscribers, etc.

  Peracto\Component\Indexer\Transport\InMemoryIndexTransport: ~
  Peracto\Component\Indexer\Transport\IndexTransportInterface: '@Peracto\Component\Indexer\Transport\InMemoryIndexTransport'

  aws.local_destination.export_orders:
    class: Peracto\Component\Utility\FileStore\LocalDestination
    arguments:
      $directory: 'var/cache/test/%env(resolve:ORDER_EXPORT_DIRECTORY)%'

  Peracto\Component\Payment\Braintree\GatewayInterface:
    class: 'Peracto\Component\Payment\Braintree\MockGateway'

  Peracto\Component\Payment\Braintree\Helper\MerchantAccountHelper:
    arguments:
      $merchantAccountIds: '%payment_braintree_merchant_account_ids%'

  App\Tests\Integration\Component\MockRecaptchaClient:
    public: true
    arguments:
      $recaptchaFixturesPath: '%kernel.project_dir%/tests/Integration/Component/Fixtures/'

  Peracto\Component\Recaptcha\Api\Verify\RecaptchaVerifyService:
    arguments:
      $client: '@App\Tests\Integration\Component\MockRecaptchaClient'
      $recaptchaSecret: 'abc123'

  Peracto\Bundle\UserBundle\EventSubscriber\RecaptchaSubscriber:
    arguments:
      $security: '@security.helper'
      $recaptchaVerifyService:  '@Peracto\Component\Recaptcha\Api\Verify\RecaptchaVerifyService'
      $recaptchaPaths: '%peracto_user.recaptcha.endpoints%'
      $recaptchaEnabled: '%peracto_user.recaptcha.enabled%'

  # Use custom OrderMailer for testing that integrates with Symfony test mailer
  Peracto\Component\Order\Mailer\OrderMailerInterface:
    class: App\Component\Order\Mailer\TestOrderMailer
    arguments:
      $mailer: '@mailer'
      $twig: '@twig'

  Peracto\Testing\Faker\Provider\UuidProvider:
    tags: [{ name: nelmio_alice.faker.provider }]
